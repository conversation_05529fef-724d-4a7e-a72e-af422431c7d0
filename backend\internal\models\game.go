package models

import (
	"time"
	"gorm.io/gorm"
)

// Game 游戏模型
type Game struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	Name          string         `json:"name" gorm:"not null"`
	Description   string         `json:"description"`
	ShortDesc     string         `json:"short_desc"`
	Developer     string         `json:"developer"`
	Publisher     string         `json:"publisher"`
	Category      string         `json:"category"`
	Tags          string         `json:"tags"` // JSON字符串存储标签数组
	Version       string         `json:"version"`
	Size          int64          `json:"size"` // 文件大小(字节)
	Rating        float64        `json:"rating" gorm:"default:0"`
	RatingCount   int            `json:"rating_count" gorm:"default:0"`
	DownloadCount int            `json:"download_count" gorm:"default:0"`
	ViewCount     int            `json:"view_count" gorm:"default:0"`
	Price         float64        `json:"price" gorm:"default:0"`
	IsFree        bool           `json:"is_free" gorm:"default:true"`
	IsActive      bool           `json:"is_active" gorm:"default:true"`
	IsFeatured    bool           `json:"is_featured" gorm:"default:false"`
	ReleaseDate   *time.Time     `json:"release_date"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Images      []GameImage    `json:"images,omitempty" gorm:"foreignKey:GameID"`
	Videos      []GameVideo    `json:"videos,omitempty" gorm:"foreignKey:GameID"`
	Reviews     []Review       `json:"reviews,omitempty" gorm:"foreignKey:GameID"`
	Posts       []Post         `json:"posts,omitempty" gorm:"foreignKey:GameID"`
	Downloads   []GameDownload `json:"downloads,omitempty" gorm:"foreignKey:GameID"`
	Library     []GameLibrary  `json:"library,omitempty" gorm:"foreignKey:GameID"`
}

// GameImage 游戏图片
type GameImage struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	GameID   uint   `json:"game_id" gorm:"not null"`
	URL      string `json:"url" gorm:"not null"`
	Type     string `json:"type"` // icon, screenshot, banner
	Order    int    `json:"order" gorm:"default:0"`
	IsActive bool   `json:"is_active" gorm:"default:true"`

	// 关联关系
	Game Game `json:"game" gorm:"foreignKey:GameID"`
}

// GameVideo 游戏视频
type GameVideo struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	GameID      uint   `json:"game_id" gorm:"not null"`
	URL         string `json:"url" gorm:"not null"`
	ThumbnailURL string `json:"thumbnail_url"`
	Title       string `json:"title"`
	Duration    int    `json:"duration"` // 秒
	Order       int    `json:"order" gorm:"default:0"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// 关联关系
	Game Game `json:"game" gorm:"foreignKey:GameID"`
}

// GameDownload 游戏下载记录
type GameDownload struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	GameID    uint      `json:"game_id" gorm:"not null"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Platform  string    `json:"platform"` // android, ios, windows
	Version   string    `json:"version"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	Game Game `json:"game" gorm:"foreignKey:GameID"`
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// GameLibrary 用户游戏库
type GameLibrary struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id" gorm:"not null"`
	GameID     uint      `json:"game_id" gorm:"not null"`
	Status     string    `json:"status"` // wishlist, downloaded, playing, completed
	PlayTime   int       `json:"play_time" gorm:"default:0"` // 游戏时长(分钟)
	LastPlayed *time.Time `json:"last_played"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Game Game `json:"game" gorm:"foreignKey:GameID"`
}

// GameStats 游戏统计信息
type GameStats struct {
	GameID        uint    `json:"game_id"`
	Rating        float64 `json:"rating"`
	RatingCount   int     `json:"rating_count"`
	DownloadCount int     `json:"download_count"`
	ViewCount     int     `json:"view_count"`
	ReviewCount   int     `json:"review_count"`
	WishlistCount int     `json:"wishlist_count"`
}

// GameSummary 游戏摘要信息
type GameSummary struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	ShortDesc     string    `json:"short_desc"`
	Developer     string    `json:"developer"`
	Category      string    `json:"category"`
	Rating        float64   `json:"rating"`
	DownloadCount int       `json:"download_count"`
	Price         float64   `json:"price"`
	IsFree        bool      `json:"is_free"`
	IconURL       string    `json:"icon_url"`
	ReleaseDate   *time.Time `json:"release_date"`
}

// ToSummary 转换为摘要信息
func (g *Game) ToSummary() GameSummary {
	iconURL := ""
	for _, img := range g.Images {
		if img.Type == "icon" && img.IsActive {
			iconURL = img.URL
			break
		}
	}

	return GameSummary{
		ID:            g.ID,
		Name:          g.Name,
		ShortDesc:     g.ShortDesc,
		Developer:     g.Developer,
		Category:      g.Category,
		Rating:        g.Rating,
		DownloadCount: g.DownloadCount,
		Price:         g.Price,
		IsFree:        g.IsFree,
		IconURL:       iconURL,
		ReleaseDate:   g.ReleaseDate,
	}
}
