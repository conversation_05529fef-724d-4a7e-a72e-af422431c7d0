package database

import (
	"context"
	"log"
	"time"

	"taptap-clone/internal/models"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize 初始化数据库连接
func Initialize(databaseURL string) (*gorm.DB, error) {
	// 配置GORM日志
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移数据库表
	if err := AutoMigrate(db); err != nil {
		return nil, err
	}

	return db, nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Follow{},
		&models.UserSession{},
		&models.Game{},
		&models.GameImage{},
		&models.GameVideo{},
		&models.GameDownload{},
		&models.GameLibrary{},
		&models.Review{},
		&models.ReviewLike{},
		&models.ReviewReply{},
		&models.ReviewReplyLike{},
		&models.Post{},
		&models.PostImage{},
		&models.PostLike{},
		&models.Comment{},
		&models.CommentLike{},
	)
}

// InitRedis 初始化Redis连接
func InitRedis(redisURL string) *redis.Client {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		log.Fatal("Failed to parse Redis URL:", err)
	}

	rdb := redis.NewClient(opt)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = rdb.Ping(ctx).Result()
	if err != nil {
		log.Fatal("Failed to connect to Redis:", err)
	}

	log.Println("Connected to Redis successfully")
	return rdb
}

// SeedData 初始化种子数据
func SeedData(db *gorm.DB) error {
	// 检查是否已有数据
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		return nil // 已有数据，跳过种子数据
	}

	// 创建示例用户
	users := []models.User{
		{
			Username:      "admin",
			Email:         "<EMAIL>",
			WalletAddress: "******************************************",
			Avatar:        "https://example.com/avatar1.jpg",
			Bio:           "TapTap管理员",
			Level:         10,
			Experience:    1000,
			IsVerified:    true,
		},
		{
			Username:      "gamer1",
			Email:         "<EMAIL>",
			WalletAddress: "******************************************",
			Avatar:        "https://example.com/avatar2.jpg",
			Bio:           "热爱游戏的玩家",
			Level:         5,
			Experience:    500,
		},
	}

	for _, user := range users {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	// 创建示例游戏
	games := []models.Game{
		{
			Name:        "原神",
			Description: "开放世界冒险游戏",
			ShortDesc:   "在提瓦特大陆开始你的冒险",
			Developer:   "miHoYo",
			Publisher:   "miHoYo",
			Category:    "RPG",
			Tags:        `["开放世界", "冒险", "RPG"]`,
			Version:     "4.2.0",
			Size:        15000000000, // 15GB
			Rating:      4.5,
			RatingCount: 10000,
			DownloadCount: 1000000,
			IsFree:      true,
			IsFeatured:  true,
			ReleaseDate: &time.Time{},
		},
		{
			Name:        "王者荣耀",
			Description: "5v5英雄公平对战手游",
			ShortDesc:   "全民MOBA手游",
			Developer:   "TiMi Studio Group",
			Publisher:   "腾讯游戏",
			Category:    "MOBA",
			Tags:        `["MOBA", "竞技", "多人"]`,
			Version:     "3.95.1",
			Size:        2000000000, // 2GB
			Rating:      4.2,
			RatingCount: 50000,
			DownloadCount: 5000000,
			IsFree:      true,
			IsFeatured:  true,
			ReleaseDate: &time.Time{},
		},
	}

	for _, game := range games {
		if err := db.Create(&game).Error; err != nil {
			return err
		}

		// 为每个游戏添加图片
		images := []models.GameImage{
			{
				GameID: game.ID,
				URL:    "https://example.com/icon_" + game.Name + ".jpg",
				Type:   "icon",
				Order:  0,
			},
			{
				GameID: game.ID,
				URL:    "https://example.com/screenshot1_" + game.Name + ".jpg",
				Type:   "screenshot",
				Order:  1,
			},
		}

		for _, image := range images {
			if err := db.Create(&image).Error; err != nil {
				return err
			}
		}
	}

	log.Println("Seed data created successfully")
	return nil
}
