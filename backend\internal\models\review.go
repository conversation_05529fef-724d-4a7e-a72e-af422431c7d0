package models

import (
	"time"
	"gorm.io/gorm"
)

// Review 游戏评论模型
type Review struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	GameID      uint           `json:"game_id" gorm:"not null"`
	Rating      int            `json:"rating" gorm:"not null;check:rating >= 1 AND rating <= 5"`
	Title       string         `json:"title"`
	Content     string         `json:"content" gorm:"not null"`
	PlayTime    int            `json:"play_time" gorm:"default:0"` // 游戏时长(分钟)
	LikesCount  int            `json:"likes_count" gorm:"default:0"`
	RepliesCount int           `json:"replies_count" gorm:"default:0"`
	IsRecommended bool         `json:"is_recommended" gorm:"default:true"`
	IsVerified   bool          `json:"is_verified" gorm:"default:false"` // 是否为认证评论
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User     User            `json:"user" gorm:"foreignKey:UserID"`
	Game     Game            `json:"game" gorm:"foreignKey:GameID"`
	Likes    []ReviewLike    `json:"likes,omitempty" gorm:"foreignKey:ReviewID"`
	Replies  []ReviewReply   `json:"replies,omitempty" gorm:"foreignKey:ReviewID"`
}

// ReviewLike 评论点赞
type ReviewLike struct {
	ID       uint      `json:"id" gorm:"primaryKey"`
	UserID   uint      `json:"user_id" gorm:"not null"`
	ReviewID uint      `json:"review_id" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	User   User   `json:"user" gorm:"foreignKey:UserID"`
	Review Review `json:"review" gorm:"foreignKey:ReviewID"`
}

// ReviewReply 评论回复
type ReviewReply struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReviewID  uint           `json:"review_id" gorm:"not null"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	ParentID  *uint          `json:"parent_id"` // 回复的回复
	Content   string         `json:"content" gorm:"not null"`
	LikesCount int           `json:"likes_count" gorm:"default:0"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Review Review           `json:"review" gorm:"foreignKey:ReviewID"`
	User   User             `json:"user" gorm:"foreignKey:UserID"`
	Parent *ReviewReply     `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Likes  []ReviewReplyLike `json:"likes,omitempty" gorm:"foreignKey:ReplyID"`
}

// ReviewReplyLike 回复点赞
type ReviewReplyLike struct {
	ID      uint      `json:"id" gorm:"primaryKey"`
	UserID  uint      `json:"user_id" gorm:"not null"`
	ReplyID uint      `json:"reply_id" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	User  User        `json:"user" gorm:"foreignKey:UserID"`
	Reply ReviewReply `json:"reply" gorm:"foreignKey:ReplyID"`
}

// ReviewSummary 评论摘要
type ReviewSummary struct {
	ID           uint        `json:"id"`
	Rating       int         `json:"rating"`
	Title        string      `json:"title"`
	Content      string      `json:"content"`
	PlayTime     int         `json:"play_time"`
	LikesCount   int         `json:"likes_count"`
	RepliesCount int         `json:"replies_count"`
	IsRecommended bool       `json:"is_recommended"`
	IsVerified   bool        `json:"is_verified"`
	CreatedAt    time.Time   `json:"created_at"`
	User         UserProfile `json:"user"`
}

// ToSummary 转换为摘要信息
func (r *Review) ToSummary() ReviewSummary {
	return ReviewSummary{
		ID:           r.ID,
		Rating:       r.Rating,
		Title:        r.Title,
		Content:      r.Content,
		PlayTime:     r.PlayTime,
		LikesCount:   r.LikesCount,
		RepliesCount: r.RepliesCount,
		IsRecommended: r.IsRecommended,
		IsVerified:   r.IsVerified,
		CreatedAt:    r.CreatedAt,
		User:         r.User.ToProfile(),
	}
}
