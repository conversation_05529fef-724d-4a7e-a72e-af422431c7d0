package models

import (
	"time"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	Username      string         `json:"username" gorm:"uniqueIndex;not null"`
	Email         string         `json:"email" gorm:"uniqueIndex"`
	WalletAddress string         `json:"wallet_address" gorm:"uniqueIndex;not null"`
	Avatar        string         `json:"avatar"`
	Bio           string         `json:"bio"`
	Level         int            `json:"level" gorm:"default:1"`
	Experience    int            `json:"experience" gorm:"default:0"`
	FollowersCount int           `json:"followers_count" gorm:"default:0"`
	FollowingCount int           `json:"following_count" gorm:"default:0"`
	IsVerified    bool           `json:"is_verified" gorm:"default:false"`
	IsActive      bool           `json:"is_active" gorm:"default:true"`
	LastLoginAt   *time.Time     `json:"last_login_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Reviews       []Review       `json:"reviews,omitempty" gorm:"foreignKey:UserID"`
	Posts         []Post         `json:"posts,omitempty" gorm:"foreignKey:UserID"`
	Comments      []Comment      `json:"comments,omitempty" gorm:"foreignKey:UserID"`
	GameLibrary   []GameLibrary  `json:"game_library,omitempty" gorm:"foreignKey:UserID"`
	Followers     []Follow       `json:"followers,omitempty" gorm:"foreignKey:FollowingID"`
	Following     []Follow       `json:"following,omitempty" gorm:"foreignKey:FollowerID"`
}

// UserProfile 用户公开资料
type UserProfile struct {
	ID             uint      `json:"id"`
	Username       string    `json:"username"`
	Avatar         string    `json:"avatar"`
	Bio            string    `json:"bio"`
	Level          int       `json:"level"`
	FollowersCount int       `json:"followers_count"`
	FollowingCount int       `json:"following_count"`
	IsVerified     bool      `json:"is_verified"`
	CreatedAt      time.Time `json:"created_at"`
}

// ToProfile 转换为公开资料
func (u *User) ToProfile() UserProfile {
	return UserProfile{
		ID:             u.ID,
		Username:       u.Username,
		Avatar:         u.Avatar,
		Bio:            u.Bio,
		Level:          u.Level,
		FollowersCount: u.FollowersCount,
		FollowingCount: u.FollowingCount,
		IsVerified:     u.IsVerified,
		CreatedAt:      u.CreatedAt,
	}
}

// Follow 关注关系模型
type Follow struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	FollowerID  uint      `json:"follower_id" gorm:"not null"`
	FollowingID uint      `json:"following_id" gorm:"not null"`
	CreatedAt   time.Time `json:"created_at"`

	// 关联关系
	Follower  User `json:"follower" gorm:"foreignKey:FollowerID"`
	Following User `json:"following" gorm:"foreignKey:FollowingID"`
}

// UserSession 用户会话
type UserSession struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}
