# TapTap Clone - 游戏发现平台

一个完全仿制TapTap的游戏发现和社区平台，包含Web端、移动端和区块链钱包登录功能。

## 技术栈

### 前端
- **Web端**: React + TypeScript + Tailwind CSS
- **移动端**: Flutter + Dart

### 后端
- **API服务**: Go + Gin + GORM
- **数据库**: PostgreSQL + Redis
- **文件存储**: MinIO/AWS S3

### 区块链集成
- **钱包登录**: Base链主网
- **支持钱包**: MetaMask, WalletConnect

## 项目结构

```
仿taptap/
├── backend/           # Go后端服务
├── web-frontend/      # React网页端
├── mobile-app/        # Flutter移动端
├── docs/             # 项目文档
├── docker/           # Docker配置
└── scripts/          # 部署脚本
```

## 核心功能模块

### 1. 用户系统
- 区块链钱包登录 (Base链)
- 用户资料管理
- 关注/粉丝系统

### 2. 游戏商店
- 游戏浏览和搜索
- 游戏详情页面
- 下载和安装
- 游戏分类和标签

### 3. 评分评论系统
- 游戏评分
- 用户评论
- 评论点赞/回复

### 4. 社区功能
- 游戏论坛
- 动态发布
- 攻略分享
- 截图/视频分享

### 5. 发现页面
- 推荐算法
- 热门游戏
- 新游预告
- 编辑推荐

## 开发计划

1. ✅ 项目架构设计
2. 🔄 TapTap平台深度分析
3. ⏳ 后端Go服务开发
4. ⏳ Web前端React开发
5. ⏳ 移动端Flutter开发
6. ⏳ Base链钱包登录集成
7. ⏳ 数据库设计与部署
8. ⏳ 测试与部署

## 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- Flutter 3.16+
- PostgreSQL 15+
- Redis 7+

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd 仿taptap

# 启动后端服务
cd backend
go mod tidy
go run main.go

# 启动Web前端
cd web-frontend
npm install
npm start

# 启动移动端 (需要模拟器或真机)
cd mobile-app
flutter pub get
flutter run
```

## 许可证

MIT License
