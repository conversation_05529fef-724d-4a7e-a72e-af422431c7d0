package config

import (
	"os"
	"strconv"
)

type Config struct {
	Environment   string
	DatabaseURL   string
	RedisURL      string
	JWTSecret     string
	Port          string
	BaseChainRPC  string
	AllowedOrigins []string
}

func Load() *Config {
	return &Config{
		Environment:   getEnv("ENVIRONMENT", "development"),
		DatabaseURL:   getEnv("DATABASE_URL", "postgres://user:password@localhost/taptap_clone?sslmode=disable"),
		RedisURL:      getEnv("REDIS_URL", "redis://localhost:6379"),
		JWTSecret:     getEnv("JWT_SECRET", "your-secret-key"),
		Port:          getEnv("PORT", "8080"),
		BaseChainRPC:  getEnv("BASE_CHAIN_RPC", "https://mainnet.base.org"),
		AllowedOrigins: []string{
			getEnv("FRONTEND_URL", "http://localhost:3000"),
			getEnv("MOBILE_URL", "http://localhost:3001"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
