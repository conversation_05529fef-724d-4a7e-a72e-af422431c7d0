package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
)

// GenerateNonce 生成随机nonce用于钱包签名
func GenerateNonce() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateSignMessage 创建用于签名的消息
func CreateSignMessage(walletAddress, nonce string) string {
	return fmt.Sprintf("Welcome to TapTap Clone!\n\nPlease sign this message to authenticate your wallet.\n\nWallet: %s\nNonce: %s", walletAddress, nonce)
}

// VerifySignature 验证钱包签名
func VerifySignature(walletAddress, message, signature string) bool {
	// 移除0x前缀
	if strings.HasPrefix(signature, "0x") {
		signature = signature[2:]
	}

	// 解码签名
	sigBytes, err := hex.DecodeString(signature)
	if err != nil {
		return false
	}

	// 以太坊签名格式调整
	if len(sigBytes) == 65 && sigBytes[64] >= 27 {
		sigBytes[64] -= 27
	}

	// 创建消息哈希
	messageHash := crypto.Keccak256Hash([]byte(fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)))

	// 恢复公钥
	pubKey, err := crypto.SigToPub(messageHash.Bytes(), sigBytes)
	if err != nil {
		return false
	}

	// 获取地址
	recoveredAddress := crypto.PubkeyToAddress(*pubKey)

	// 比较地址
	return strings.EqualFold(recoveredAddress.Hex(), walletAddress)
}

// IsValidEthereumAddress 验证以太坊地址格式
func IsValidEthereumAddress(address string) bool {
	if !strings.HasPrefix(address, "0x") {
		return false
	}

	// 检查长度
	if len(address) != 42 {
		return false
	}

	// 检查是否为有效的十六进制
	matched, _ := regexp.MatchString("^0x[0-9a-fA-F]{40}$", address)
	return matched
}

// NormalizeAddress 标准化地址格式
func NormalizeAddress(address string) string {
	if !IsValidEthereumAddress(address) {
		return ""
	}
	return common.HexToAddress(address).Hex()
}

// IsBaseChainAddress 检查是否为Base链地址（实际上Base链使用相同的地址格式）
func IsBaseChainAddress(address string) bool {
	return IsValidEthereumAddress(address)
}
